// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/image_type.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/bottom_sheet_helper.dart';
import 'package:hodan_hospital/features/doctor/domain/entities/doctor_entity.dart';
import 'package:hodan_hospital/features/doctor/presentation/pages/patient_appointment_confirm_page.dart';
import 'package:hodan_hospital/features/shared/data/models/gender.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_item_wrapper.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_button.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_image_picker_card.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_textfield.dart';
import 'package:hodan_hospital/features/user/domain/entities/district_entity.dart';
import 'package:hodan_hospital/features/user/domain/entities/users_by_phone_entity.dart';
import 'package:hodan_hospital/features/user/presentation/bloc/user_bloc.dart';

import '../../../../core/utils/helpers/snack_bar_helper.dart';

class PatientAppointnentPage extends StatefulWidget {
  final DoctorEntity doctor;
  const PatientAppointnentPage({super.key, required this.doctor});

  @override
  State<PatientAppointnentPage> createState() => _PatientAppointnentPageState();
}

class _PatientAppointnentPageState extends State<PatientAppointnentPage> {
  final appointmentFormKey = GlobalKey<FormState>();
  final TextEditingController _pIDController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController patientNameController = TextEditingController();
  final TextEditingController ageController = TextEditingController();

  UsersByPhoneEntity? selectedUser;
  Gender? selectedGender;
  DistrictEntity? selectedDistrict;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.wait([
        _loadUsers(),
        _loadDistricts(),
      ]);
    });
  }

  Future<void> _loadUsers({
    bool forceFetch = false,
  }) async {
    final currentUser = context.userBloc.currentUser;
    final phoneNumber = currentUser?.phoneNumber ?? '';
    final familyMembers = context.userBloc.usersByPhone;
    final canRefreshFamilyMembers = forceFetch || familyMembers.isEmpty;
    if (canRefreshFamilyMembers) {
      context.userBloc.add(
        GetUsersByPhoneNumberEvent(
          phoneNumber: phoneNumber,
          doctorName: widget.doctor.name,
        ),
      );

      // Wait for users to load, then set the first user by default
      await context.userBloc.stream.firstWhere(
        (state) {
          return state is UsersLoaded || state is UserFailure;
        },
      ).then((_) {
        if (mounted) {
          final firstUser = context.userBloc.usersByPhone.firstOrNull;
          if (firstUser != null) {
            setState(() {
              selectedUser = firstUser;
              _pIDController.text = selectedUser?.pID ?? '';
              _nameController.text = selectedUser?.firstName ?? '';
            });
          }
        }
      });
    }
  }

  Future<void> _loadDistricts({bool forceFetch = false}) async {
    final districts = context.userBloc.districts;
    final canRefreshDistricts = forceFetch || districts.isEmpty;
    if (canRefreshDistricts) {
      context.userBloc.add(const GetDistrictsEvent(forceFetch: true));
    }
  }

  @override
  void dispose() {
    _pIDController.dispose();
    _nameController.dispose();
    patientNameController.dispose();
    ageController.dispose();
    selectedUser = null;
    selectedGender = null;
    selectedDistrict = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final userBloc = context.userBloc;
    final currentUserPhone = userBloc.currentUser?.phoneNumber ?? '';

    return BlocConsumer<UserBloc, UserState>(
      listener: (context, state) {
        if (state is UserRegistrationLoading) {
          context.dialogCubit.showLoadingDialog();
        }

        if (state is UserFailure) {
          context.dialogCubit.closeDialog();

          //
          final message = state.appFailure.getErrorMessage();
          SnackBarHelper.showErrorSnackBar(
            context,
            message: message,
          );
        }

        if (state is UserRegistered) {
          context.dialogCubit.closeDialog();

          // refresh
          userBloc.add(GetUsersByPhoneNumberEvent(
            phoneNumber: currentUserPhone,
            doctorName: widget.doctor.name,
          ));

          // show success snackbar
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: state.message,
          );

          // clear fields and pop
          patientNameController.clear();
          ageController.clear();
          Navigator.pop(context);
        }
      },
      builder: (context, state) {
        final usersByPhone = userBloc.usersByPhone;
        final isLoading = state is UserRegistrationLoading;
        return Scaffold(
          body: Column(
            children: [
              // Fixed Header Section
              CustomAppBar(
                title: 'Patient',
                trailing: InkWell(
                  onTap: () {
                    // show add new patient bottom sheet
                    BottomSheetHelper.instance.showAddNewPatientBottomSheet(
                      context: context,
                      isLoading: isLoading,
                      isDismissible: !isLoading,
                      enableDrag: !isLoading,
                      ageController: ageController,
                      nameController: patientNameController,
                      onPatientAdd: () {
                        //  adding a new patient
                        userBloc.add(RegisterNewPatientEvent(
                          fullName: patientNameController.text.trim(),
                          mobileNumber: currentUserPhone,
                          age: double.parse(ageController.text.trim()),
                          ageType: 'Year',
                          district: selectedDistrict?.name ?? '',
                          gender: selectedGender?.genderType ?? '',
                        ));
                      },
                      onDistrictSelected: (value) {
                        setState(() {
                          selectedDistrict = value;
                        });
                      },
                      onGenderSelected: (value) {
                        setState(() {
                          selectedGender = value;
                        });
                      },
                      selectedDistrict: selectedDistrict,
                      selectedGender: selectedGender,
                    );
                  },
                  child: Container(
                    height: 32,
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      color:
                          context.appColors.primaryColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.person_add_rounded,
                          size: 16,
                          color: context.appColors.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'New Patient',
                          style: TextStyle(
                            color: context.appColors.primaryColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Scrollable Content Section
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await Future.wait([
                      _loadUsers(forceFetch: true),
                      _loadDistricts(forceFetch: true),
                    ]);
                  },
                  child: Form(
                    key: appointmentFormKey,
                    child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 20.h),
                          // CustomButton(
                          //   buttonText: "Add new",
                          //   width: context.screenWidth * 0.8,
                          //   height: 40,
                          //   onTap: () {
                          //     // show add new patient bottom sheet
                          //     BottomSheetHelper.instance.showAddNewPatientBottomSheet(
                          //       context: context,
                          //       isLoading: isLoading,
                          //       isDismissible: !isLoading,
                          //       enableDrag: !isLoading,
                          //       ageController: ageController,
                          //       nameController: patientNameController,
                          //       onPatientAdd: () {
                          //         //  adding a new patient
                          //         userBloc.add(RegisterNewPatientEvent(
                          //           fullName: patientNameController.text.trim(),
                          //           mobileNumber: currentUserPhone,
                          //           age: double.parse(ageController.text.trim()),
                          //           ageType: "Year",
                          //           district: selectedDistrict?.name ?? '',
                          //           gender: selectedGender?.genderType ?? '',
                          //         ));
                          //       },
                          //       onDistrictSelected: (value) {
                          //         setState(() {
                          //           selectedDistrict = value;
                          //         });
                          //       },
                          //       onGenderSelected: (value) {
                          //         setState(() {
                          //           selectedGender = value;
                          //         });
                          //       },
                          //       selectedDistrict: selectedDistrict,
                          //       selectedGender: selectedGender,
                          //     );
                          //   },
                          // ),
                          SizedBox(height: 20.h),
                          // DoctorInformationWidget(
                          //   butonIsNeeded: false,
                          //   widget: widget.doctor,
                          //   onTap: () {
                          //     //
                          //   },
                          // ),

                          SizedBox(height: 20.h),
                          Padding(
                            padding: EdgeInsets.only(left: 20.w),
                            child: AnimatedItemWrapper(
                                delay: const Duration(milliseconds: 500),
                                child: Text(
                                  'Appointment For :',
                                  style: textTheme.titleMedium,
                                )),
                          ),
                          SizedBox(height: 10.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20.w),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 10.h),
                                AnimatedItemWrapper(
                                  delay: const Duration(milliseconds: 600),
                                  child: CustomTextField(
                                    isReadOnly: true,
                                    controller: _pIDController,
                                    labelText: 'Patient ID',
                                    // validator: (value) {
                                    //   if (value!.isEmpty) {
                                    //     return 'Please enter patient id';
                                    //   }
                                    //   return null;
                                    // },
                                  ),
                                ),
                                SizedBox(height: 15.h),
                                AnimatedItemWrapper(
                                  delay: const Duration(milliseconds: 700),
                                  child: CustomTextField(
                                    isReadOnly: true,
                                    controller: _nameController,
                                    labelText: 'Patient name',
                                    // validator: (value) {
                                    //   if (value!.isEmpty) {
                                    //     return 'Please enter patient name';
                                    //   }
                                    //   return null;
                                    // },
                                  ),
                                ),
                                Container(
                                  height: 15.h,
                                  color: context.appColors.backgroundColor,
                                ),
                                Container(
                                  width: double.infinity,
                                  // color: Colors.grey,
                                  color: context.appColors.backgroundColor,
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.w),
                                  child: AnimatedItemWrapper(
                                    delay: const Duration(milliseconds: 800),
                                    child: Text(
                                      'Who is the patient?',
                                      style: textTheme.titleSmall,
                                    ),
                                  ),
                                ),

                                /// Enhanced Patient List with better design
                                Container(
                                  height: 280.h,
                                  margin: EdgeInsets.symmetric(vertical: 10.h),
                                  decoration: BoxDecoration(
                                    color: context.appColors.cardColor,
                                    borderRadius: BorderRadius.circular(16.r),
                                    boxShadow: [
                                      BoxShadow(
                                        color: context.appColors.subtextColor
                                            .withValues(alpha: 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: CustomListGridView(
                                    items: usersByPhone,
                                    isLoading: state is UserLoading,
                                    isEmpty: state is UsersLoaded &&
                                        state.user.isEmpty,
                                    contentType: LoadingType.listView,
                                    showFooter: false,
                                    shrinkWrap: false,
                                    physics:
                                        const AlwaysScrollableScrollPhysics(),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 16.w, vertical: 12.h),
                                    emptyWidgetMessage:
                                        'No patients found.\nPull down to refresh or add a new patient.',
                                    itemBuilder: (context, user) {
                                      final isSelected = selectedUser == user;
                                      return AnimatedContainer(
                                        duration:
                                            const Duration(milliseconds: 200),
                                        margin: EdgeInsets.only(bottom: 8.h),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? context.appColors.primaryColor
                                                  .withValues(alpha: 0.1)
                                              : context
                                                  .appColors.backgroundColor,
                                          borderRadius:
                                              BorderRadius.circular(12.r),
                                          border: Border.all(
                                            color: isSelected
                                                ? context.appColors.primaryColor
                                                : context.appColors.dividerColor
                                                    .withValues(alpha: 0.3),
                                            width: isSelected ? 2 : 1,
                                          ),
                                        ),
                                        child: ListTile(
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: 16.w,
                                            vertical: 8.h,
                                          ),
                                          leading: Container(
                                            width: 50.w,
                                            height: 50.h,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              border: Border.all(
                                                color: isSelected
                                                    ? context
                                                        .appColors.primaryColor
                                                    : context
                                                        .appColors.dividerColor
                                                        .withValues(alpha: 0.3),
                                                width: 2,
                                              ),
                                            ),
                                            child: CustomImagePickerCard(
                                              imageUrl: user.patientImage,
                                              userName: user.firstName,
                                              imageType: ImageType.profile,
                                              radius: 25,
                                            ),
                                          ),
                                          title: Text(
                                            user.firstName,
                                            style: context.textTheme.bodyLarge
                                                ?.copyWith(
                                              color: isSelected
                                                  ? context
                                                      .appColors.primaryColor
                                                  : context.textTheme.bodyLarge
                                                      ?.color,
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.w500,
                                            ),
                                          ),
                                          subtitle: Text(
                                            'ID: ${user.pID}',
                                            style: context.textTheme.bodySmall
                                                ?.copyWith(
                                              color: context
                                                  .appColors.subtextColor,
                                            ),
                                          ),
                                          trailing: isSelected
                                              ? Icon(
                                                  Icons.check_circle,
                                                  color: context
                                                      .appColors.primaryColor,
                                                  size: 24.w,
                                                )
                                              : Icon(
                                                  Icons.radio_button_unchecked,
                                                  color: context
                                                      .appColors.dividerColor,
                                                  size: 24.w,
                                                ),
                                          onTap: () {
                                            setState(() {
                                              selectedUser = user;
                                              _pIDController.text = user.pID;
                                              _nameController.text =
                                                  user.firstName;
                                            });
                                          },
                                        ),
                                      );
                                    },
                                    onRefresh: () async {
                                      await Future.wait([
                                        _loadUsers(forceFetch: true),
                                      ]);
                                    },
                                  ),
                                ),
                                // Bottom padding to account for fixed button
                                SizedBox(height: 100.h),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Fixed Bottom Button Section
              Container(
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: context.appColors.backgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color:
                          context.appColors.subtextColor.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SafeArea(
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    child: CustomButton(
                      buttonText: selectedUser != null
                          ? 'Book Appointment for ${selectedUser!.firstName}'
                          : 'Select Patient to Book',
                      width: double.infinity,
                      height: 50.h,
                      buttonState: selectedUser != null
                          ? ButtonState.normal
                          : ButtonState.disabled,
                      onTap: () {
                        if (selectedUser == null) {
                          SnackBarHelper.showErrorSnackBar(
                            context,
                            message:
                                'Please select a patient before proceeding with the appointment booking.',
                          );
                          return;
                        }

                        if (appointmentFormKey.currentState!.validate()) {
                          // Navigate to the booking page with the selected user's details
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  PatientAppointmentConfirmPage(
                                doctor: widget.doctor,
                                user: selectedUser!,
                              ),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
